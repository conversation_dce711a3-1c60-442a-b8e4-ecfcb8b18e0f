"use client";

import React, { useMemo } from "react";
import { FinancialSummaryCard } from "@/shared/UI/user-management/FinancialSummaryCard";
import { useUserFinancialReportContext } from "../contexts/UserFinancialReportContext";

/**
 * Financial summary cards component that displays totals from the optimized API
 * Uses shared context with 4 separate API calls for optimal performance
 *
 * Features:
 * - Displays Total Deposit, Total Withdraw, and Total Bets from summary API (API 4)
 * - Automatically updates when filters change in the financial report
 * - Uses the optimized summary API response for accurate filtered data
 */
export const UserFinancialSummaryCards: React.FC = () => {
  // Use the shared financial report context with optimized data
  const { summaryData, totalDeposits, totalWithdrawals, isLoading } = useUserFinancialReportContext();

  const summaryCardsData = useMemo(() => [
    {
      id: 'total-deposit',
      svgName: 'balance' as const,
      title: 'Total Deposit',
      value: totalDeposits,
    },
    {
      id: 'total-withdraw',
      svgName: 'deposite' as const,
      title: 'Total Withdraw',
      value: totalWithdrawals,
    },
    {
      id: 'total-bets',
      svgName: 'withdraw' as const,
      title: 'Total Bets',
      value: summaryData?.totals?.totalBet || 0,
      currency: ''
    }
  ], [totalDeposits, totalWithdrawals, summaryData?.totals?.totalBet]);

  return (
    <div className="flex-1 lg:flex-[0.25] flex flex-col gap-5">
      {summaryCardsData.map((cardData) => (
        <FinancialSummaryCard
          key={cardData.id}
          svgName={cardData.svgName}
          title={cardData.title}
          value={cardData.value}
          {...(cardData.currency !== undefined && { currency: cardData.currency })}
        />
      ))}
    </div>
  );
};
