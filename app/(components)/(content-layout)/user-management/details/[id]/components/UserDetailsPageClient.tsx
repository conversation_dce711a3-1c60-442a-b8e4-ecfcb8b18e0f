// app/(components)/(content-layout)/user-management/details/[id]/components/UserDetailsPageClient.tsx - Client-side component for user details
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useUserDetails } from "@/shared/hooks";
import { TabNavigation, UserProfileHeader, CardSkeleton, SpkErrorMessage, WalletTransactionModal } from "@/shared/UI/components";
import React, { Fragment, useState } from "react";
import UserAccountSummaryContent from "./UserAccountSummaryContent";
import UserLoginHistoryContent from "./UserLoginHistoryContent";

// Import extracted components
import { TabType } from "./UserDetailsTabNavigation";

// Import user-specific financial report component
import { UserStatisticsSection } from "@/shared/UI/user-management/UserStatisticsSection";
import UserFinancialReportContent from "./UserFinancialReportContent";
import { UserFinancialReportProvider } from "../contexts/UserFinancialReportContext";
import { UserStatisticsProvider, useUserStatisticsContext } from "../contexts/UserStatisticsContext";
import { UserFinancialSummaryCards } from "./UserFinancialSummaryCards";

interface Props {
	userId: string;
}

/**
 * Wrapper component for UserStatisticsSection that uses dedicated statistics context
 * This prevents duplicate API calls by using a separate context for overview data
 */
const UserStatisticsSectionWithContext: React.FC<{
	userData: any;
	onDepositClick: () => void;
	onWithdrawClick: () => void;
}> = ({ userData, onDepositClick, onWithdrawClick }) => {
	const { isOverviewLoading, totalDeposits, totalWithdrawals, totalBets } = useUserStatisticsContext();

	// Extract transaction totals from overview data
	const transactionTotals = {
		totalDeposit: totalDeposits,
		totalWithdraw: totalWithdrawals,
		totalBet: totalBets
	};

	return (
		<UserStatisticsSection
			userData={userData}
			onDepositClick={onDepositClick}
			onWithdrawClick={onWithdrawClick}
			transactionTotals={transactionTotals}
			isTotalsLoading={isOverviewLoading}
		/>
	);
};

/**
 * Client-side component that handles all interactive functionality for user details
 * Redesigned with 4 main sections as per specifications:
 * 1. User Profile Header (144px height)
 * 2. Statistics Cards (158px height)
 * 3. Tab Navigation (67px height)
 * 4. Tab Content Area
 */
export function UserDetailsPageClient({ userId }: Props) {
	// State for active tab
	const [activeTab, setActiveTab] = useState<TabType>('financial-report');

	// Use custom hook for all business logic
	const {
		userData,
		isLoading,
		isError,
		error,
		handleRefresh,
		isAuthenticated,
		hasHydrated,
		userTypeLabel,
		openWalletModal,
		isWalletModalOpen,
		walletTransactionType,
		closeWalletModal
	} = useUserDetails({ userId });

	// Note: Financial report data and summary cards are now handled by UserFinancialReportProvider context
	// Bet report and login history data are handled by their respective components

	// Don't render anything if not authenticated and hydrated
	if (hasHydrated && !isAuthenticated) {
		return null;
	}

	// Show loading state with skeleton
	if (isLoading) {
		return <CardSkeleton layout="single" showAvatar={true} showActions={true} showStats={true} />;
	}

	// Show error state
	if (isError || !userData) {
		return (
			<Fragment>
				<SpkErrorMessage
					message={error?.message || "Failed to load user details"}
					onRetry={handleRefresh}
					variant="box"
					size="md"
					title="Error Loading Data"
				/>
			</Fragment>
		);
	}

	return (
		<Fragment>
			{/* Wrap statistics section in its own provider for overview data */}
			<UserStatisticsProvider userId={userId}>
				{/* Wrap financial report in its own provider for report data */}
				<UserFinancialReportProvider userId={userId}>
					{/* Main Content with 4 Sections Layout */}
					<div className={`flex flex-col gap-5 ${fadeInStyles.fadeIn}`}>
						{/* Section 1: User Profile Header (144px height) */}
						<UserProfileHeader
							userData={userData}
							userTypeLabel={userTypeLabel}
							showWalletBalance={true}
						/>

						{/* Section 2: Statistics Cards (158px height) - Uses dedicated statistics context */}
						<UserStatisticsSectionWithContext
							userData={userData}
							onDepositClick={() => openWalletModal('deposit')}
							onWithdrawClick={() => openWalletModal('withdraw')}
						/>

						{/* Section 3: Tab Navigation (67px height) */}
						<TabNavigation
							tabs={[
								{ id: 'financial-report', label: 'Financial Report' },
								{ id: 'account-statement', label: 'Account Statement' },
								{ id: 'login-history', label: 'Login History' }
							]}
							activeTab={activeTab}
							onTabChange={(tab) => setActiveTab(tab as TabType)}
							variant="golden"
							radius={"16px"}
						/>

						{/* Section 4: Tab Content Area */}
						<div className="min-h-[400px]">
							{activeTab === 'financial-report' && (
								<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
									{/* 70% section - Financial Report Components */}
									<div className="flex-1 lg:flex-[0.75]">
										<UserFinancialReportContent />
									</div>

									{/* 30% section - Summary Cards */}
									<UserFinancialSummaryCards />
								</div>
							)}

							{activeTab === 'account-statement' && (
								<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
									{/* Account Summary Content - using imported component */}
									<div className="flex-1">
										<UserAccountSummaryContent userId={userId} />
									</div>
								</div>
							)}

							{activeTab === 'login-history' && (
								<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
									{/* Login History Content - using imported component */}
									<div className="flex-1">
										<UserLoginHistoryContent userId={userId} />
									</div>
								</div>
							)}
						</div>
				</UserFinancialReportProvider>
			</UserStatisticsProvider>

			{/* Wallet Transaction Modal - Pre-configured for user context */}
			<WalletTransactionModal
				isOpen={isWalletModalOpen}
				onClose={closeWalletModal}
				transactionType={walletTransactionType || 'deposit'}
				preSelectedUser={userData ? {
					id: userData.id.toString(),
					userName: userData.userName,
					walletId: userData.walletid || ''
				} : undefined}
				showUserSelection={false} // Hide user selection since user is pre-selected
				context="user-details" // Indicate this is from user details context
				balance={userData?.amount || 0} // Pass current balance for display
				onSuccess={() => {
					// Refresh user data after successful transaction
					handleRefresh();
					closeWalletModal();
				}}
			/>
		</Fragment>
	);
};
