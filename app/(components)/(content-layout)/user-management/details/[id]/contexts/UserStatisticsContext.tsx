"use client";

import { useOptimizedUserFinancialReport } from "@/shared/hooks/business/useOptimizedUserFinancialReport";
import { FinancialReportResponse } from "@/shared/types/report-types";
import { useUserDetailsQuery } from "@/shared/query/useUserDetailsQuery";
import React, { createContext, useContext, ReactNode } from "react";

interface UserStatisticsContextType {
  // Overview data for statistics section
  overviewData: FinancialReportResponse | null;
  isOverviewLoading: boolean;
  
  // Computed values for statistics cards
  totalDeposits: number;
  totalWithdrawals: number;
  totalBets: number;

  // Actions
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

const UserStatisticsContext = createContext<UserStatisticsContextType | undefined>(undefined);

interface UserStatisticsProviderProps {
  userId: string;
  children: ReactNode;
}

/**
 * Context provider for user statistics data
 * Uses overview API call: user created date to today with totals=true
 * This is separate from the financial report context to avoid unnecessary API calls
 */
export const UserStatisticsProvider: React.FC<UserStatisticsProviderProps> = ({
  userId,
  children
}) => {
  // Fetch user details to get the created date
  const { data: userDetailsResponse } = useUserDetailsQuery(userId);
  const userCreatedDate = userDetailsResponse?.data?.createdAt;

  // Use the optimized financial report hook with only overview data
  const statisticsData = useOptimizedUserFinancialReport({
    userId,
    userCreatedDate,
    includeOverview: true, // Only fetch overview data
    initialFilters: {}
  });

  // Create context value
  const contextValue: UserStatisticsContextType = {
    // Overview data
    overviewData: statisticsData.overviewData,
    isOverviewLoading: statisticsData.isOverviewLoading,
    
    // Computed values from overview data
    totalDeposits: statisticsData.overviewData?.totals?.totalDeposit || 0,
    totalWithdrawals: statisticsData.overviewData?.totals?.totalWithdraw || 0,
    totalBets: statisticsData.overviewData?.totals?.totalBet || 0,

    // Actions
    handleRefresh: statisticsData.handleRefresh,

    // Authentication state
    isAuthenticated: statisticsData.isAuthenticated,
    hasHydrated: statisticsData.hasHydrated
  };

  return (
    <UserStatisticsContext.Provider value={contextValue}>
      {children}
    </UserStatisticsContext.Provider>
  );
};

/**
 * Hook to use the user statistics context
 */
export const useUserStatisticsContext = (): UserStatisticsContextType => {
  const context = useContext(UserStatisticsContext);
  if (context === undefined) {
    throw new Error('useUserStatisticsContext must be used within a UserStatisticsProvider');
  }
  return context;
};
