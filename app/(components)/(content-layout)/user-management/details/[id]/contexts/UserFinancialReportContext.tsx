"use client";

import { useOptimizedUserFinancialReport } from "@/shared/hooks/business/useOptimizedUserFinancialReport";
import { FinancialReportFilters, FinancialReportResponse, DEFAULT_FINANCIAL_REPORT_FILTERS } from "@/shared/types/report-types";
import { useUserDetailsQuery } from "@/shared/query/useUserDetailsQuery";
import React, { createContext, useContext, ReactNode } from "react";

interface UserFinancialReportContextType {
  // State
  filters: FinancialReportFilters;

  // Data from 4 separate API calls
  overviewData: FinancialReportResponse | null; // API 1: Overview cards (user created date to today with totals)
  financialReportResponse: FinancialReportResponse | null; // API 2: Financial report list (dateRange filter) - for backward compatibility
  listData: FinancialReportResponse | null; // API 2: Financial report list (dateRange filter)
  totalCount: number; // API 3: Pagination count
  summaryData: FinancialReportResponse | null; // API 4: Summary cards (dateRange filter with totals)

  // Loading states
  isLoading: boolean;
  isOverviewLoading: boolean;
  isListLoading: boolean;
  isCountLoading: boolean;
  isSummaryLoading: boolean;
  isAnyLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values for summary cards (from summaryData)
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

const UserFinancialReportContext = createContext<UserFinancialReportContextType | undefined>(undefined);

interface UserFinancialReportProviderProps {
  userId: string;
  children: ReactNode;
}

/**
 * Context provider for user financial report data
 * Uses optimized API calls with exactly 3 API calls:
 * 1. Financial report list: dateRange filter without totals
 * 2. Pagination count: same filters as list with count=true
 * 3. Summary cards: dateRange filter with totals=true
 *
 * Features:
 * - Shared filters and data between components
 * - Optimized API calls to avoid unnecessary requests
 * - Filter synchronization across components
 * - Overview data is handled separately by UserStatisticsProvider
 */
export const UserFinancialReportProvider: React.FC<UserFinancialReportProviderProps> = ({
  userId,
  children
}) => {
  // Fetch user details to get the created date
  const { data: userDetailsResponse } = useUserDetailsQuery(userId);
  const userCreatedDate = userDetailsResponse?.data?.createdAt;

  // Use the optimized financial report hook with only 3 API calls (no overview)
  const financialReportData = useOptimizedUserFinancialReport({
    userId,
    userCreatedDate,
    includeOverview: false, // Don't include overview data - handled by UserStatisticsProvider
    initialFilters: {
      ...DEFAULT_FINANCIAL_REPORT_FILTERS,
      // Override timeType to use custom range from user creation to today
      timeType: 'custom',
      // The hook will automatically set the date range based on userCreatedDate
    }
  });

  // Create context value with backward compatibility
  const contextValue: UserFinancialReportContextType = {
    ...financialReportData,
    // Maintain backward compatibility by mapping listData to financialReportResponse
    financialReportResponse: financialReportData.listData,
    // Map computed values for backward compatibility
    totalTransactions: financialReportData.totalCount,
    totalAmount: financialReportData.summaryData?.totalAmount || 0,
    // Use summary data for the summary cards
    totalDeposits: financialReportData.totalDeposits,
    totalWithdrawals: financialReportData.totalWithdrawals,
    // Map loading states
    isLoading: financialReportData.isAnyLoading,
    isFetching: financialReportData.isAnyLoading
  };

  return (
    <UserFinancialReportContext.Provider value={contextValue}>
      {children}
    </UserFinancialReportContext.Provider>
  );
};

/**
 * Hook to access the user financial report context
 * Must be used within a UserFinancialReportProvider
 */
export const useUserFinancialReportContext = (): UserFinancialReportContextType => {
  const context = useContext(UserFinancialReportContext);

  if (context === undefined) {
    throw new Error('useUserFinancialReportContext must be used within a UserFinancialReportProvider');
  }

  return context;
};
