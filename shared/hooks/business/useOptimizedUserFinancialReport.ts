// shared/hooks/business/useOptimizedUserFinancialReport.ts
"use client";

import { useAuthStore } from "@/shared/stores/authStore";
import { DEFAULT_FINANCIAL_REPORT_FILTERS, FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import {
  useFinancialReportOverview,
  useFinancialReportList,
  useFinancialReportCount,
  useFinancialReportSummary
} from "@/shared/query/useOptimizedFinancialReportQuery";

interface UseOptimizedUserFinancialReportOptions {
  userId: string;
  userCreatedDate?: string;
  initialFilters?: Partial<FinancialReportFilters>;
  includeOverview?: boolean; // Whether to include overview data for statistics section
}

interface UseOptimizedUserFinancialReportReturn {
  // State
  filters: FinancialReportFilters;

  // Data from API calls
  overviewData: FinancialReportResponse | null; // API 1: Overview cards (user created date to today with totals) - only if includeOverview is true
  listData: FinancialReportResponse | null; // API 2: Financial report list (dateRange filter)
  totalCount: number; // API 3: Pagination count
  summaryData: FinancialReportResponse | null; // API 4: Summary cards (dateRange filter with totals)

  // Loading states
  isOverviewLoading: boolean;
  isListLoading: boolean;
  isCountLoading: boolean;
  isSummaryLoading: boolean;
  isAnyLoading: boolean;

  // Error states
  isError: boolean;
  error: any;

  // Computed values for summary cards (from summaryData)
  totalDeposits: number;
  totalWithdrawals: number;
  totalBets: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

/**
 * Optimized hook for user financial reports with conditional API calls:
 * - Overview cards: user created date to today with totals=true (only if includeOverview is true)
 * - Financial report list: dateRange filter without totals
 * - Pagination count: same filters as list with count=true
 * - Summary cards: dateRange filter with totals=true
 */
export const useOptimizedUserFinancialReport = (
  options: UseOptimizedUserFinancialReportOptions
): UseOptimizedUserFinancialReportReturn => {
  const { userId, userCreatedDate, initialFilters, includeOverview = false } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters with user-specific playerId
  const [filters, setFilters] = useState<FinancialReportFilters>(() => ({
    ...DEFAULT_FINANCIAL_REPORT_FILTERS,
    ...initialFilters,
    playerId: userId
  }));

  // API Call 1: Overview cards (user created date to today with totals) - only if includeOverview is true
  const {
    data: overviewData,
    isLoading: isOverviewLoading,
    error: overviewError,
    refetch: refetchOverview
  } = useFinancialReportOverview(
    { playerId: userId },
    userCreatedDate,
    includeOverview // Pass the includeOverview flag
  );

  // API Call 2: Financial report list (dateRange filter without totals)
  const {
    data: listData,
    isLoading: isListLoading,
    error: listError,
    refetch: refetchList
  } = useFinancialReportList(filters);

  // API Call 3: Pagination count (same filters as list with count=true)
  const {
    data: totalCount,
    isLoading: isCountLoading,
    error: countError,
    refetch: refetchCount
  } = useFinancialReportCount(filters);

  // API Call 4: Summary cards (dateRange filter with totals=true)
  const {
    data: summaryData,
    isLoading: isSummaryLoading,
    error: summaryError,
    refetch: refetchSummary
  } = useFinancialReportSummary(filters);

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<FinancialReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      playerId: userId // Always maintain userId
    }));
  }, [userId]);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh - refetch all APIs
  const handleRefresh = useCallback(() => {
    refetchOverview();
    refetchList();
    refetchCount();
    refetchSummary();
  }, [refetchOverview, refetchList, refetchCount, refetchSummary]);

  // Computed values
  const isAnyLoading = isOverviewLoading || isListLoading || isCountLoading || isSummaryLoading;
  const isError = !!(overviewError || listError || countError || summaryError);
  const error = overviewError || listError || countError || summaryError;

  // Summary card values from summaryData (API 4)
  const totalDeposits = summaryData?.totals?.totalDeposit || 0;
  const totalWithdrawals = summaryData?.totals?.totalWithdraw || 0;
  const totalBets = summaryData?.totals?.totalBet || 0;

  return {
    // State
    filters,

    // Data from 4 separate API calls
    overviewData: overviewData || null,
    listData: listData || null,
    totalCount: totalCount || 0,
    summaryData: summaryData || null,

    // Loading states
    isOverviewLoading,
    isListLoading,
    isCountLoading,
    isSummaryLoading,
    isAnyLoading,

    // Error states
    isError,
    error,

    // Computed values for summary cards
    totalDeposits,
    totalWithdrawals,
    totalBets,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};
