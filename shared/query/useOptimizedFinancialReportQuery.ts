// shared/query/useOptimizedFinancialReportQuery.ts
import { useAuthStore } from '@/shared/stores/authStore';
import {
  FinancialReportFilters,
  FinancialReportResponse
} from '@/shared/types/report-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { generateTimePeriod } from './utils';

/**
 * Build query string for financial report API with specific parameters
 */
const buildOptimizedFinancialReportQuery = (
  filters: FinancialReportFilters,
  options: {
    includeTotal?: boolean;
    includeCount?: boolean;
    useUserCreatedDate?: boolean;
    userCreatedDate?: string;
  } = {}
): string => {
  const queryParts: string[] = [];
  
  // Required parameters
  queryParts.push(`size=${filters.size}`);
  queryParts.push(`page=${filters.page}`);
  queryParts.push('actionCategory=financial');

  // Handle timePeriod based on options
  let timePeriod: string;
  
  if (options.useUserCreatedDate && options.userCreatedDate) {
    // For overview cards: from user created date to today
    const today = new Date();
    const todayFormatted = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;
    
    timePeriod = generateTimePeriod({
      startDate: options.userCreatedDate,
      endDate: todayFormatted
    });
  } else if (filters.dateRange) {
    // Use selected dateRange filter
    timePeriod = generateTimePeriod(filters.dateRange);
  } else {
    // Default to today
    const today = new Date();
    const todayStart = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;
    const todayEnd = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;
    
    timePeriod = generateTimePeriod({
      startDate: todayStart,
      endDate: todayEnd
    });
  }
  
  queryParts.push(`timePeriod=${timePeriod}`);

  // Default parameters
  queryParts.push(`order=${filters.order || 'desc'}`);
  queryParts.push(`sortBy=${filters.sortBy || 'created_at'}`);
  queryParts.push(`timeType=${filters.timeType || 'today'}`);

  // Add playerId for user-specific queries
  if (filters.playerId) {
    queryParts.push(`playerId=${filters.playerId}`);
  }

  // Add other filters if present
  if (filters.actionType) {
    queryParts.push(`actionType=${filters.actionType}`);
  }
  if (filters.transactionId) {
    queryParts.push(`transactionId=${filters.transactionId}`);
  }
  if (filters.gameProvider) {
    queryParts.push(`gameProvider=${filters.gameProvider}`);
  }
  if (filters.gameType) {
    queryParts.push(`gameType=${filters.gameType}`);
  }

  // Add totals parameter if requested
  if (options.includeTotal) {
    queryParts.push('totals=true');
  }

  // Add count parameter if requested
  if (options.includeCount) {
    queryParts.push('count=true');
  }

  return queryParts.join('&');
};

/**
 * Fetch financial report with specific options
 */
export const fetchOptimizedFinancialReport = async (
  filters: FinancialReportFilters,
  options: {
    includeTotal?: boolean;
    includeCount?: boolean;
    useUserCreatedDate?: boolean;
    userCreatedDate?: string;
  } = {}
): Promise<FinancialReportResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Build query string
  const queryString = buildOptimizedFinancialReportQuery(filters, options);

  // Use the correct endpoint for financial reports
  const response = await fetch(`https://reporting.ingrandstation.com/api/v2/admin/transactions?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch financial report: ${response.status}`);
  }

  const responseData = await response.json();
  const result = responseData.data || responseData;

  // Transform the response to match expected format
  return {
    data: result.result || result.data || [],
    success: responseData.success || 1,
    message: responseData.message || 'Success',
    errors: responseData.errors || [],
    count: result.count || result.result?.length || 0,
    totalPages: result.totalPages || Math.ceil((result.count || 0) / filters.size),
    currentPage: filters.page,
    totalAmount: result.totalAmount || 0,
    totalDeposits: result.totalDeposits || 0,
    totalWithdrawals: result.totalWithdrawals || 0,
    totals: result.totals ? {
      totalDeposit: result.totals.total_deposit || 0,
      totalWithdraw: result.totals.total_withdraw || 0,
      totalBet: result.totals.total_bet || 0
    } : undefined
  };
};

/**
 * Hook for overview cards (user created date to today with totals)
 */
export const useFinancialReportOverview = (
  filters: Partial<FinancialReportFilters>,
  userCreatedDate?: string
) => {
  const { token } = useAuthStore();

  return useQuery<FinancialReportResponse>({
    queryKey: ['financialReportOverview', filters.playerId, userCreatedDate],
    queryFn: async () => {
      const finalFilters: FinancialReportFilters = {
        size: 1, // We only need totals, not data
        page: 1,
        order: 'desc',
        sortBy: 'created_at',
        actionCategory: 'financial',
        timeType: 'custom',
        ...filters,
      };

      return await fetchOptimizedFinancialReport(finalFilters, {
        includeTotal: true,
        useUserCreatedDate: true,
        userCreatedDate
      });
    },
    enabled: !!token && !!userCreatedDate && !!filters.playerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for financial report list (with dateRange filter)
 */
export const useFinancialReportList = (filters: Partial<FinancialReportFilters>) => {
  const { token } = useAuthStore();

  const finalFilters: FinancialReportFilters = {
    size: 25,
    page: 1,
    order: 'desc',
    sortBy: 'created_at',
    actionCategory: 'financial',
    timeType: 'today',
    ...filters,
  };

  return useQuery<FinancialReportResponse>({
    queryKey: ['financialReportList', finalFilters],
    queryFn: async () => {
      return await fetchOptimizedFinancialReport(finalFilters, {
        includeTotal: false,
        includeCount: false
      });
    },
    enabled: !!token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    placeholderData: keepPreviousData,
  });
};

/**
 * Hook for financial report count (pagination)
 */
export const useFinancialReportCount = (filters: Partial<FinancialReportFilters>) => {
  const { token } = useAuthStore();

  const finalFilters: FinancialReportFilters = {
    size: 1, // We only need count, not data
    page: 1,
    order: 'desc',
    sortBy: 'created_at',
    actionCategory: 'financial',
    timeType: 'today',
    ...filters,
  };

  return useQuery<number>({
    queryKey: ['financialReportCount', finalFilters],
    queryFn: async () => {
      const response = await fetchOptimizedFinancialReport(finalFilters, {
        includeCount: true
      });
      return response.count || 0;
    },
    enabled: !!token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    placeholderData: keepPreviousData,
  });
};

/**
 * Hook for financial report summary (with totals and dateRange filter)
 */
export const useFinancialReportSummary = (filters: Partial<FinancialReportFilters>) => {
  const { token } = useAuthStore();

  const finalFilters: FinancialReportFilters = {
    size: 1, // We only need totals, not data
    page: 1,
    order: 'desc',
    sortBy: 'created_at',
    actionCategory: 'financial',
    timeType: 'today',
    ...filters,
  };

  return useQuery<FinancialReportResponse>({
    queryKey: ['financialReportSummary', finalFilters],
    queryFn: async () => {
      return await fetchOptimizedFinancialReport(finalFilters, {
        includeTotal: true,
        includeCount: false
      });
    },
    enabled: !!token,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    placeholderData: keepPreviousData,
  });
};
