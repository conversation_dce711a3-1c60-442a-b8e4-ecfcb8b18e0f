// shared/query/useFinancialReportQuery.ts
import { useAuthStore } from '@/shared/stores/authStore';
import {
  DEFAULT_FINANCIAL_REPORT_FILTERS,
  FinancialReportFilters,
  FinancialReportResponse
} from '@/shared/types/report-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';
import { generateTimePeriod } from './utils';

/**
 * Map transaction type numbers to readable action types
 */
export const CODE_TO_TRANSACTION_TYPES = {
  0: 'DEBIT',
  1: 'CREDIT',
  2: 'ROLLBACK',
  3: 'DEPOSIT',
  4: 'WITHDRAW',
  14: 'WITHDRAW_CANCEL',
};

const getActionTypeFromTransactionType = (transactionType: number): string => {
  const typeMap: { [key: number]: string } = {
    ...CODE_TO_TRANSACTION_TYPES,
    21: 'bet', // Bet transaction
    22: 'win', // Win transaction
  };
  return typeMap[transactionType] || `type_${transactionType}`;
};

/**
 * Generate description from meta_data array for bet transactions
 */
const generateDescriptionFromMetaData = (metaData: any[]): string => {
  if (!metaData || !Array.isArray(metaData) || metaData.length === 0) {
    return '';
  }

  // For bet transactions, create a description from the events
  const events = metaData.map(item => `${item.event} - ${item.market}: ${item.outcome}`);
  return events.join('; ');
};

/**
 * Build query string for financial report API
 */
const buildFinancialReportQuery = (filters: FinancialReportFilters): string => {
  const queryParts: string[] = [];

  // Required parameters
  queryParts.push(`size=${filters.size}`);
  queryParts.push(`page=${filters.page}`);

  // Required actionCategory=financial parameter
  queryParts.push('actionCategory=financial');

  // Required timePeriod parameter with exact format
  let timePeriod: string;
  if (filters.dateRange && filters.dateRange.startDate && filters.dateRange.endDate) {
    // Use the dateRange filter values
    timePeriod = generateTimePeriod(filters.dateRange);
  } else {
    // Default to today
    timePeriod = generateTimePeriod();
  }
  queryParts.push(`timePeriod=${timePeriod}`);

  // Default parameters
  queryParts.push(`order=${filters.order || 'desc'}`);
  queryParts.push(`sortBy=${filters.sortBy || 'created_at'}`);
  queryParts.push(`timeType=${filters.timeType || 'today'}`);
  // queryParts.push(`timeZone=${encodeURIComponent(filters.timeZone || 'UTC +00:00')}`);
  // queryParts.push(`timeZoneName=${encodeURIComponent(filters.timeZoneName || 'UTC +00:00')}`);
  // queryParts.push(`lifetimeRecords=${filters.lifetimeRecords || false}`);

  // DateTime as URL encoded empty object - use proper encoding
  // queryParts.push('dateTime=%7B%7D'); // URL encoded {}

  // queryParts.push('totals=true');

  // Add optional filters if provided
  // Handle dateRange conversion to startDate/endDate
  if (filters.dateRange && filters.dateRange.startDate && filters.dateRange.endDate) {
    queryParts.push(`startDate=${encodeURIComponent(filters.dateRange.startDate)}`);
    queryParts.push(`endDate=${encodeURIComponent(filters.dateRange.endDate)}`);
  }

  if (filters.gameProvider) {
    queryParts.push(`gameProvider=${encodeURIComponent(filters.gameProvider)}`);
  }

  if (filters.gameType) {
    queryParts.push(`gameType=${encodeURIComponent(filters.gameType)}`);
  }

  if (filters.search) {
    queryParts.push(`search=${encodeURIComponent(filters.search)}`);
  }

  if (filters.roundId) {
    queryParts.push(`roundId=${encodeURIComponent(filters.roundId)}`);
  }

  if (filters.amount) {
    queryParts.push(`amount=${encodeURIComponent(filters.amount)}`);
  }

  if (filters.transactionId) {
    queryParts.push(`transactionId=${encodeURIComponent(filters.transactionId)}`);
  }

  if (filters.debitTransactionId) {
    queryParts.push(`debitTransactionId=${encodeURIComponent(filters.debitTransactionId)}`);
  }

  if (filters.utrNumber) {
    queryParts.push(`utrNumber=${encodeURIComponent(filters.utrNumber)}`);
  }

  if (filters.tenantId) {
    queryParts.push(`tenantId=${encodeURIComponent(filters.tenantId)}`);
  }

  if (filters.currencyId) {
    queryParts.push(`currencyId=${encodeURIComponent(filters.currencyId)}`);
  }

  if (filters.actionType) {
    // Transform actionType from numeric value to title string for API
    const actionTypeTitle = transformActionTypeForApi(filters.actionType);
    if (actionTypeTitle) {
      // Don't encode the array brackets and quotes - API expects raw format like ["withdraw"]
      queryParts.push(`actionType=${actionTypeTitle}`);
    }
  }

  if (filters.playerId) {
    queryParts.push(`playerId=${encodeURIComponent(filters.playerId)}`);
  }

  if (timePeriod) {
    queryParts.push(`dateTime=${timePeriod}`);
  }

  // Add totals parameter if requested
  if (filters.totals) {
    queryParts.push('totals=true');
  }

  return queryParts.join('&');
};

export const fetchFinancialReport = async (filters: FinancialReportFilters): Promise<FinancialReportResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the staging backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Build query string
  const queryString = buildFinancialReportQuery(filters);

  // Use the correct endpoint for financial reports
  const response = await fetch(`https://reporting.ingrandstation.com/api/v2/admin/transactions?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch financial report: ${response.status}`);
  }

  const apiResponse = await response.json();

  // Transform to standardized format
  const result = apiResponse.data?.data?.result;
  const rawRows = result?.rows || [];

  // Transform each row to match FinancialTransaction interface
  const transformedData = rawRows.map((row: any) => ({
    id: row.internal_tracking_id,
    transactionId: row.transaction_id,
    userName: row.from_wallet_uname || row.action_by_uname || 'Unknown',
    actionType: getActionTypeFromTransactionType(row.transaction_type),
    actionCategory: 'financial',
    amount: parseFloat(row.amount) || 0,
    currency: row.currency || row.source_currency_id,
    totalBalance: parseFloat(row.ending_balance) || 0,
    status: row.status || 'unknown',
    createdAt: row.created_at,
    description: generateDescriptionFromMetaData(row.meta_data) || row.comments || 'No description',
    gameProvider: row.game_provider,
    gameType: row.game_name
  }));

  return {
    data: transformedData,
    success: apiResponse.success,
    message: apiResponse.data?.message || '',
    errors: apiResponse.errors || [],
    count: result?.count,
    totalPages: result?.total_pages,
    currentPage: result?.current_page,
    totalAmount: transformedData.reduce((sum: number, transaction: any) => sum + (transaction.amount || 0), 0) || 0,
    totalDeposits: transformedData.filter((t: any) => t.actionType === 'deposit').reduce((sum: number, transaction: any) => sum + (transaction.amount || 0), 0) || 0,
    totalWithdrawals: transformedData.filter((t: any) => t.actionType === 'withdrawal').reduce((sum: number, transaction: any) => sum + (transaction.amount || 0), 0) || 0,
    // Include totals object from API response if available
    totals: result?.totals ? {
      totalDeposit: result.totals.total_deposit || 0,
      totalWithdraw: result.totals.total_withdraw || 0,
      totalBet: result.totals.total_bet || 0
    } : undefined
  };
};

export const useFinancialReportQuery = (filters: Partial<FinancialReportFilters> = {}, initialData?: FinancialReportResponse | null) => {
  const { token } = useAuthStore();

  // Build final filters with defaults
  const finalFilters: FinancialReportFilters = {
    ...DEFAULT_FINANCIAL_REPORT_FILTERS,
    ...filters,
  };
  return useQuery<FinancialReportResponse>({
    queryKey: ['financialReport', finalFilters],
    queryFn: async () => {
      try {
        return await fetchFinancialReport(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated
    staleTime: 2 * 60 * 1000, // Data considered fresh for 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    initialData: initialData || undefined,
    placeholderData: keepPreviousData, // Maintain previous data during filter changes to prevent full-page skeleton loading
  });
};

// Hook for refetching financial report with new filters
export const useFinancialReportRefetch = () => {
  return (filters: Partial<FinancialReportFilters> = {}) => {
    const finalFilters: FinancialReportFilters = {
      ...DEFAULT_FINANCIAL_REPORT_FILTERS,
      ...filters,
    };

    return fetchFinancialReport(finalFilters);
  };
};
